{"cesVersion": "2.18.3", "projectName": "ppapp", "packages": [{"name": "expo-router", "type": "navigation", "options": {"type": "tabs"}}, {"name": "stylesheet", "type": "styling"}], "flags": {"noGit": false, "noInstall": false, "overwrite": false, "importAlias": true, "packageManager": "npm", "eas": false, "publish": false}, "packageManager": {"type": "npm", "version": "10.9.2"}, "os": {"type": "Windows_NT", "platform": "win32", "arch": "x64", "kernelVersion": "10.0.26100"}}