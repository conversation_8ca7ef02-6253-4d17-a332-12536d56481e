{"name": "ppapp", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"android": "expo start --android", "ios": "expo start --ios", "start": "expo start", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@gorhom/bottom-sheet": "^5.1.5", "@react-navigation/native": "^7.0.3", "expo": "^53.0.9", "expo-constants": "~17.1.4", "expo-linking": "~7.1.4", "expo-router": "~5.0.3", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.6", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "ajv": "^8.12.0", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "prettier": "^3.2.5", "typescript": "~5.8.3"}, "private": true}